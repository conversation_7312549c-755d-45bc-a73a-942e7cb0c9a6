<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('programs', function (Blueprint $table) {
            $table->dropColumn('type');
            $table->after('name', function (Blueprint $table) {
                $table->foreignId('type_id')->nullable()->constrained('program_types')->onDelete('set null');
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('programs', function (Blueprint $table) {
            $table->after('name', function (Blueprint $table) {
                $table->string('type', 50)->nullable();
            });
            $table->dropForeign(['type_id']);
            $table->dropColumn('type_id');
        });
    }
};
