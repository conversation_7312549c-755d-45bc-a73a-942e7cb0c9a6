# All Tables Documentation
# Generated from Laravel Migration Files
#
# This file contains information about all database tables created and modified
# by the migration files in this directory.

## Database Tables Overview

Generated on: 2025-01-20
Total migration files: 170+

## Tables Created by Migrations

### Core System Tables

**users** (2014_10_12_000000_create_users_table.php)
- id (primary key)
- uuid (unique index)
- name (nullable string)
- email (unique string)
- username (50 chars, nullable, unique)
- email_verified_at (nullable timestamp)
- password (nullable string)
- status (20 chars, nullable)
- preference (json, nullable)
- remember_token
- pending_update (json, nullable)
- meta (json, nullable)
- timestamps

**password_resets** (2014_10_12_100000_create_password_resets_table.php)
- email (string, index)
- token (string)
- created_at (nullable timestamp)

**failed_jobs** (2019_08_19_000000_create_failed_jobs_table.php)
- id (primary key)
- uuid (unique string)
- connection (text)
- queue (text)
- payload (longtext)
- exception (longtext)
- failed_at (timestamp, default current)

**personal_access_tokens** (2019_12_14_000001_create_personal_access_tokens_table.php)
- id (primary key)
- tokenable_type (string)
- tokenable_id (big integer)
- name (string)
- token (64 chars, unique)
- abilities (text, nullable)
- last_used_at (nullable timestamp)
- expires_at (nullable timestamp)
- timestamps

**activity_log** (2021_04_02_133851_create_activity_log_table.php)
- id (primary key)
- log_name (nullable string)
- description (text)
- subject_type (nullable string)
- event (nullable string)
- subject_id (nullable big integer)
- causer_type (nullable string)
- causer_id (nullable big integer)
- properties (json, nullable)
- batch_uuid (nullable uuid)
- timestamps

**jobs** (2021_04_02_135516_create_jobs_table.php)
- id (primary key)
- queue (string, index)
- payload (longtext)
- attempts (unsigned tiny integer)
- reserved_at (nullable unsigned integer)
- available_at (unsigned integer)
- created_at (unsigned integer)

**teams** (2021_10_03_063033_create_teams_table.php)
- id (primary key)
- uuid (unique index)
- name (100 chars, nullable)
- slug (100 chars, nullable)
- description (text, nullable)
- config (json, nullable)
- meta (json, nullable)
- timestamps

### Permission & Role Tables

**permissions** (2021_04_02_134344_create_permission_tables.php)
- id (primary key)
- name (string)
- guard_name (string)
- timestamps

**roles** (2021_04_02_134344_create_permission_tables.php)
- id (primary key)
- name (string)
- guard_name (string)
- timestamps

**model_has_permissions** (2021_04_02_134344_create_permission_tables.php)
- permission_id (foreign key)
- model_type (string)
- model_id (big integer)

**model_has_roles** (2021_04_02_134344_create_permission_tables.php)
- role_id (foreign key)
- model_type (string)
- model_id (big integer)

**role_has_permissions** (2021_04_02_134344_create_permission_tables.php)
- permission_id (foreign key)
- role_id (foreign key)

### Content Management Tables

**tags** (2021_04_03_133553_create_tags_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- slug (100 chars, nullable)
- description (text, nullable)
- meta (json, nullable)
- timestamps

**taggables** (2021_04_03_133628_create_taggables_table.php)
- tag_id (foreign key)
- taggable_type (string)
- taggable_id (big integer)

**comments** (2021_04_03_133705_create_comments_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- user_id (foreign key to users, nullable)
- commentable_type (string, nullable)
- commentable_id (big integer, nullable)
- comment (text, nullable)
- meta (json, nullable)
- timestamps

**configs** (2021_04_04_100237_create_configs_table.php)
- id (primary key)
- name (100 chars, nullable)
- team_id (foreign key to teams, nullable) [Added in update migration]
- value (text, nullable)
- timestamps

**medias** (2021_04_07_133811_create_medias_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- user_id (foreign key to users, nullable)
- name (255 chars, nullable)
- file_name (255 chars, nullable)
- mime_type (100 chars, nullable)
- size (integer, nullable)
- meta (json, nullable)
- timestamps

### Task Management Tables

**options** (2021_10_03_133737_create_options_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- user_id (foreign key to users, nullable)
- name (100 chars, nullable)
- slug (100 chars, nullable)
- description (text, nullable)
- color (20 chars, nullable)
- meta (json, nullable)
- timestamps

**todos** (2021_10_03_133823_create_todos_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- user_id (foreign key to users, nullable)
- title (255 chars, nullable)
- description (text, nullable)
- due_date (date, nullable)
- due_time (time, nullable)
- completed_at (datetime, nullable)
- archived_at (datetime, nullable)
- meta (json, nullable)
- timestamps

### Template & Security Tables

**templates** (2022_10_15_095734_create_templates_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- user_id (foreign key to users, nullable)
- name (255 chars, nullable)
- type (50 chars, nullable)
- category (50 chars, nullable)
- content (longtext, nullable)
- meta (json, nullable)
- timestamps

**failed_login_attempts** (2022_12_28_025905_create_failed_login_attempts_table.php)
- id (primary key)
- email (string, nullable)
- ip_address (45 chars, nullable)
- user_agent (text, nullable)
- attempted_at (timestamp, default current)

### Academic System Tables

**programs** (2023_05_07_064841_create_programs_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- type (50 chars, nullable)
- code (50 chars, nullable)
- shortcode (50 chars, nullable)
- alias (100 chars, nullable)
- description (text, nullable)
- config (json, nullable)
- meta (json, nullable)
- timestamps

**periods** (2023_05_08_135007_create_periods_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- code (50 chars, nullable)
- shortcode (50 chars, nullable)
- alias (100 chars, nullable)
- description (text, nullable)
- start_date (date, nullable)
- end_date (date, nullable)
- config (json, nullable)
- meta (json, nullable)
- timestamps

**divisions** (2023_05_08_135129_create_divisions_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- code (50 chars, nullable)
- shortcode (50 chars, nullable)
- alias (100 chars, nullable)
- description (text, nullable)
- config (json, nullable)
- meta (json, nullable)
- timestamps

**courses** (2023_05_08_135155_create_courses_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- division_id (foreign key to divisions, nullable)
- name (100 chars, nullable)
- code (50 chars, nullable)
- shortcode (50 chars, nullable)
- alias (100 chars, nullable)
- description (text, nullable)
- config (json, nullable)
- meta (json, nullable)
- timestamps

**batches** (2023_05_08_135213_create_batches_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- course_id (foreign key to courses, nullable)
- name (100 chars, nullable)
- code (50 chars, nullable)
- shortcode (50 chars, nullable)
- alias (100 chars, nullable)
- description (text, nullable)
- config (json, nullable)
- meta (json, nullable)
- timestamps

**subjects** (2023_05_08_135255_create_subjects_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- code (50 chars, nullable)
- shortcode (50 chars, nullable)
- alias (100 chars, nullable)
- description (text, nullable)
- type (50 chars, nullable)
- position (integer, nullable)
- has_grading (boolean, default false)
- credit_unit (decimal 8,2, nullable)
- config (json, nullable)
- meta (json, nullable)
- timestamps

**subject_records** (2023_05_08_140137_create_subject_records_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- subject_id (foreign key to subjects)
- batch_id (foreign key to batches)
- employee_id (foreign key to employees, nullable)
- config (json, nullable)
- meta (json, nullable)
- timestamps

### Financial Management Tables

**ledger_types** (2023_05_28_132728_create_ledger_types_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- alias (100 chars, nullable)
- type (50 chars, nullable)
- description (text, nullable)
- config (json, nullable)
- meta (json, nullable)
- timestamps

**ledgers** (2023_05_28_132740_create_ledgers_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- ledger_type_id (foreign key to ledger_types, nullable)
- name (100 chars, nullable)
- alias (100 chars, nullable)
- type (50 chars, nullable)
- description (text, nullable)
- config (json, nullable)
- meta (json, nullable)
- timestamps

**payment_methods** (2023_05_28_165225_create_payment_methods_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- slug (100 chars, nullable)
- description (text, nullable)
- is_payment_gateway (boolean, default false)
- config (json, nullable)
- meta (json, nullable)
- timestamps

**transactions** (2023_05_29_014604_create_transactions_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- number_format (string, nullable)
- number (string, nullable)
- prefix (string, nullable)
- suffix (string, nullable)
- type (50 chars, nullable)
- head (100 chars, nullable)
- amount (decimal 25,5, default 0)
- date (date, nullable)
- remarks (text, nullable)
- upload_token (uuid, nullable)
- user_id (foreign key to users, nullable)
- processed_at (datetime, nullable)
- cancelled_at (datetime, nullable)
- config (json, nullable)
- meta (json, nullable)
- timestamps

**transaction_records** (2023_05_29_014621_create_transaction_records_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- transaction_id (foreign key to transactions)
- ledger_id (foreign key to ledgers)
- amount (decimal 25,5, default 0)
- meta (json, nullable)
- timestamps

**transaction_payments** (2023_05_29_061344_create_transaction_payments_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- transaction_id (foreign key to transactions)
- payment_method_id (foreign key to payment_methods)
- amount (decimal 25,5, default 0)
- gateway (string, nullable)
- reference_number (string, nullable)
- status (20 chars, nullable)
- gateway_response (json, nullable)
- meta (json, nullable)
- timestamps

### Fee Management Tables

**fee_groups** (2023_05_31_093350_create_fee_groups_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- description (text, nullable)
- meta (json, nullable)
- timestamps

**fee_heads** (2023_05_31_093534_create_fee_heads_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- fee_group_id (foreign key to fee_groups)
- name (100 chars, nullable)
- description (text, nullable)
- position (integer, nullable)
- meta (json, nullable)
- timestamps

**transport_circles** (2023_06_01_110212_create_transport_circles_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- description (text, nullable)
- meta (json, nullable)
- timestamps

**transport_fees** (2023_06_05_053550_create_transport_fees_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- transport_circle_id (foreign key to transport_circles)
- period_id (foreign key to periods)
- amount (decimal 25,5, default 0)
- meta (json, nullable)
- timestamps

**transport_fee_records** (2023_06_05_053616_create_transport_fee_records_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- transport_fee_id (foreign key to transport_fees)
- batch_id (foreign key to batches)
- meta (json, nullable)
- timestamps

**fee_concessions** (2023_06_05_071845_create_fee_concessions_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- type (50 chars, nullable)
- value (decimal 25,5, default 0)
- description (text, nullable)
- meta (json, nullable)
- timestamps

**fee_concession_records** (2023_06_05_071948_create_fee_concession_records_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- fee_concession_id (foreign key to fee_concessions)
- fee_head_id (foreign key to fee_heads)
- meta (json, nullable)
- timestamps

**fee_structures** (2023_06_06_045931_create_fee_structures_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- batch_id (foreign key to batches)
- period_id (foreign key to periods)
- transport_circle_id (foreign key to transport_circles, nullable)
- meta (json, nullable)
- timestamps

**fee_installments** (2023_06_06_045955_create_fee_installments_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- fee_structure_id (foreign key to fee_structures)
- title (100 chars, nullable)
- due_date (date, nullable)
- late_fee (decimal 25,5, default 0)
- meta (json, nullable)
- timestamps

**fee_installment_records** (2023_06_06_050012_create_fee_installment_records_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- fee_installment_id (foreign key to fee_installments)
- fee_head_id (foreign key to fee_heads)
- amount (decimal 25,5, default 0)
- is_optional (boolean, default false)
- meta (json, nullable)
- timestamps

**fee_allocations** (2023_06_06_050028_create_fee_allocations_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- student_id (foreign key to students)
- fee_structure_id (foreign key to fee_structures)
- fee_concession_id (foreign key to fee_concessions, nullable)
- meta (json, nullable)
- timestamps

### Contact & Student Management Tables

**contacts** (2023_07_07_055441_create_contacts_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- contact_number (20 chars, nullable)
- email (100 chars, nullable)
- birth_date (date, nullable)
- anniversary_date (date, nullable)
- gender (20 chars, nullable)
- blood_group (10 chars, nullable)
- religion (50 chars, nullable)
- category (50 chars, nullable)
- caste (50 chars, nullable)
- mother_tongue (50 chars, nullable)
- nationality (50 chars, nullable)
- birth_place (100 chars, nullable)
- marital_status (20 chars, nullable)
- present_address (json, nullable)
- permanent_address (json, nullable)
- emergency_contact (json, nullable)
- unique_id_number1 (50 chars, nullable)
- unique_id_number2 (50 chars, nullable)
- unique_id_number3 (50 chars, nullable)
- photo (string, nullable)
- upload_token (uuid, nullable)
- user_id (foreign key to users, nullable)
- meta (json, nullable)
- timestamps

**registrations** (2023_07_11_054250_create_registrations_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- period_id (foreign key to periods)
- contact_id (foreign key to contacts)
- number_format (string, nullable)
- number (string, nullable)
- prefix (string, nullable)
- suffix (string, nullable)
- date (date, nullable)
- fee (decimal 25,5, default 0)
- status (20 chars, nullable)
- remarks (text, nullable)
- upload_token (uuid, nullable)
- meta (json, nullable)
- timestamps

**admissions** (2023_07_11_054351_create_admissions_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- registration_id (foreign key to registrations, nullable)
- batch_id (foreign key to batches)
- number_format (string, nullable)
- number (string, nullable)
- prefix (string, nullable)
- suffix (string, nullable)
- date (date, nullable)
- leaving_date (date, nullable)
- remarks (text, nullable)
- upload_token (uuid, nullable)
- meta (json, nullable)
- timestamps

**students** (2023_07_11_054529_create_students_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- admission_id (foreign key to admissions)
- contact_id (foreign key to contacts)
- roll_number (50 chars, nullable)
- joining_date (date, nullable)
- leaving_date (date, nullable)
- meta (json, nullable)
- timestamps

**guardians** (2023_07_11_054541_create_guardians_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- student_id (foreign key to students)
- contact_id (foreign key to contacts)
- relation (50 chars, nullable)
- primary_contact (boolean, default false)
- meta (json, nullable)
- timestamps

### Employee Management Tables

**departments** (2023_07_19_063856_create_departments_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- alias (100 chars, nullable)
- description (text, nullable)
- parent_id (foreign key to departments, nullable)
- meta (json, nullable)
- timestamps

**designations** (2023_07_19_063930_create_designations_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- alias (100 chars, nullable)
- description (text, nullable)
- parent_id (foreign key to designations, nullable)
- meta (json, nullable)
- timestamps

**employees** (2023_07_19_063956_create_employees_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- contact_id (foreign key to contacts)
- code_number (50 chars, nullable)
- joining_date (date, nullable)
- leaving_date (date, nullable)
- meta (json, nullable)
- timestamps

**employee_records** (2023_07_19_064055_create_employee_records_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- employee_id (foreign key to employees)
- department_id (foreign key to departments, nullable)
- designation_id (foreign key to designations, nullable)
- start_date (date, nullable)
- end_date (date, nullable)
- remarks (text, nullable)
- meta (json, nullable)
- timestamps

**accounts** (2023_07_19_095711_create_accounts_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- contact_id (foreign key to contacts)
- number (50 chars, nullable)
- name (100 chars, nullable)
- bank_name (100 chars, nullable)
- branch_name (100 chars, nullable)
- bank_identification_code (50 chars, nullable)
- meta (json, nullable)
- timestamps

**documents** (2023_07_19_095721_create_documents_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- contact_id (foreign key to contacts)
- title (100 chars, nullable)
- type (50 chars, nullable)
- start_date (date, nullable)
- end_date (date, nullable)
- file (string, nullable)
- meta (json, nullable)
- timestamps

**qualifications** (2023_07_19_095732_create_qualifications_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- contact_id (foreign key to contacts)
- title (100 chars, nullable)
- institute (100 chars, nullable)
- affiliated_to (100 chars, nullable)
- course_type (50 chars, nullable)
- level (50 chars, nullable)
- result (50 chars, nullable)
- start_date (date, nullable)
- end_date (date, nullable)
- meta (json, nullable)
- timestamps

**experiences** (2023_07_19_095900_create_experiences_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- contact_id (foreign key to contacts)
- title (100 chars, nullable)
- company_name (100 chars, nullable)
- location (100 chars, nullable)
- job_profile (text, nullable)
- start_date (date, nullable)
- end_date (date, nullable)
- meta (json, nullable)
- timestamps

### Student Fee Management Tables

**student_fees** (2023_07_21_155629_create_student_fees_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- student_id (foreign key to students)
- fee_installment_id (foreign key to fee_installments)
- amount (decimal 25,5, default 0)
- paid (decimal 25,5, default 0)
- balance (decimal 25,5, default 0)
- due_date (date, nullable)
- late_fee (decimal 25,5, default 0)
- status (20 chars, nullable)
- meta (json, nullable)
- timestamps

**student_fee_records** (2023_07_21_155654_create_student_fee_records_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- student_fee_id (foreign key to student_fees)
- fee_head_id (foreign key to fee_heads)
- amount (decimal 25,5, default 0)
- paid (decimal 25,5, default 0)
- balance (decimal 25,5, default 0)
- concession (decimal 25,5, default 0)
- is_optional (boolean, default false)
- meta (json, nullable)
- timestamps

### Event & Calendar Management Tables

**holidays** (2023_07_23_072009_create_holidays_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- date (date, nullable)
- description (text, nullable)
- meta (json, nullable)
- timestamps

**events** (2023_07_23_072230_create_events_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- user_id (foreign key to users, nullable)
- title (255 chars, nullable)
- start_date (date, nullable)
- end_date (date, nullable)
- start_time (time, nullable)
- end_time (time, nullable)
- venue (255 chars, nullable)
- description (text, nullable)
- type (50 chars, nullable)
- meta (json, nullable)
- timestamps

### Communication Tables

**correspondences** (2023_07_25_054622_create_correspondences_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- user_id (foreign key to users, nullable)
- type (50 chars, nullable)
- subject (255 chars, nullable)
- body (longtext, nullable)
- recipient (json, nullable)
- sent_at (datetime, nullable)
- upload_token (uuid, nullable)
- meta (json, nullable)
- timestamps

**incharges** (2023_07_25_123846_create_incharges_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- model_type (string, nullable)
- model_id (big integer, nullable)
- employee_id (foreign key to employees)
- start_date (date, nullable)
- end_date (date, nullable)
- remarks (text, nullable)
- meta (json, nullable)
- timestamps

### Vehicle Management Tables

**vehicles** (2023_07_30_024306_create_vehicles_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- registration_number (50 chars, nullable)
- model (100 chars, nullable)
- make (100 chars, nullable)
- color (50 chars, nullable)
- fuel_type (50 chars, nullable)
- max_seating_capacity (integer, nullable)
- contact_person (100 chars, nullable)
- contact_number (20 chars, nullable)
- meta (json, nullable)
- timestamps

**vehicle_travel_records** (2023_07_30_024732_create_vehicle_travel_records_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- vehicle_id (foreign key to vehicles)
- log_date (date, nullable)
- start_reading (decimal 25,5, default 0)
- end_reading (decimal 25,5, default 0)
- distance (decimal 25,5, default 0)
- start_place (100 chars, nullable)
- end_place (100 chars, nullable)
- purpose (text, nullable)
- driver (100 chars, nullable)
- meta (json, nullable)
- timestamps

**vehicle_fuel_records** (2023_07_30_024751_create_vehicle_fuel_records_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- vehicle_id (foreign key to vehicles)
- log_date (date, nullable)
- volume (decimal 25,5, default 0)
- price_per_unit (decimal 25,5, default 0)
- amount (decimal 25,5, default 0)
- reading (decimal 25,5, default 0)
- vendor (100 chars, nullable)
- meta (json, nullable)
- timestamps

**vehicle_service_records** (2023_07_30_024822_create_vehicle_service_records_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- vehicle_id (foreign key to vehicles)
- log_date (date, nullable)
- amount (decimal 25,5, default 0)
- reading (decimal 25,5, default 0)
- next_due_date (date, nullable)
- next_due_reading (decimal 25,5, default 0)
- vendor (100 chars, nullable)
- description (text, nullable)
- meta (json, nullable)
- timestamps

### Library Management Tables

**books** (2023_07_30_140924_create_books_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- title (255 chars, nullable)
- isbn (50 chars, nullable)
- author (255 chars, nullable)
- publisher (255 chars, nullable)
- subject (100 chars, nullable)
- language (50 chars, nullable)
- edition (50 chars, nullable)
- volume (50 chars, nullable)
- page (integer, nullable)
- price (decimal 25,5, default 0)
- summary (text, nullable)
- type (50 chars, nullable)
- location (100 chars, nullable)
- meta (json, nullable)
- timestamps

**book_additions** (2023_07_30_141104_create_book_additions_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- book_id (foreign key to books)
- date (date, nullable)
- quantity (integer, default 0)
- price (decimal 25,5, default 0)
- vendor (100 chars, nullable)
- remarks (text, nullable)
- meta (json, nullable)
- timestamps

**book_copies** (2023_07_30_142005_create_book_copies_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- book_id (foreign key to books)
- number (50 chars, nullable)
- condition (50 chars, nullable)
- location (100 chars, nullable)
- remarks (text, nullable)
- meta (json, nullable)
- timestamps

**book_transactions** (2023_07_30_142036_create_book_transactions_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- number_format (string, nullable)
- number (string, nullable)
- prefix (string, nullable)
- suffix (string, nullable)
- type (50 chars, nullable)
- date (date, nullable)
- return_date (date, nullable)
- returned_at (datetime, nullable)
- user_id (foreign key to users, nullable)
- member_type (string, nullable)
- member_id (big integer, nullable)
- remarks (text, nullable)
- meta (json, nullable)
- timestamps

**book_transaction_records** (2023_07_30_142259_create_book_transaction_records_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- book_transaction_id (foreign key to book_transactions)
- book_copy_id (foreign key to book_copies)
- meta (json, nullable)
- timestamps

### Facility Management Tables

**blocks** (2023_07_31_125255_create_blocks_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- alias (100 chars, nullable)
- description (text, nullable)
- meta (json, nullable)
- timestamps

**floors** (2023_07_31_126255_create_floors_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- block_id (foreign key to blocks)
- name (100 chars, nullable)
- alias (100 chars, nullable)
- description (text, nullable)
- meta (json, nullable)
- timestamps

**rooms** (2023_07_31_127255_create_rooms_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- floor_id (foreign key to floors)
- name (100 chars, nullable)
- number (50 chars, nullable)
- alias (100 chars, nullable)
- description (text, nullable)
- capacity (integer, nullable)
- area (decimal 25,5, default 0)
- meta (json, nullable)
- timestamps

**inventories** (2023_07_31_128255_create_inventories_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- room_id (foreign key to rooms, nullable)
- name (100 chars, nullable)
- code (50 chars, nullable)
- model (100 chars, nullable)
- brand (100 chars, nullable)
- specification (text, nullable)
- date_of_purchase (date, nullable)
- date_of_warranty (date, nullable)
- price (decimal 25,5, default 0)
- vendor (100 chars, nullable)
- condition (50 chars, nullable)
- meta (json, nullable)
- timestamps

### Stock Management Tables

**stock_categories** (2023_07_31_130255_create_stock_categories_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- name (100 chars, nullable)
- description (text, nullable)
- parent_id (foreign key to stock_categories, nullable)
- meta (json, nullable)
- timestamps

**stock_items** (2023_07_31_130308_create_stock_items_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- stock_category_id (foreign key to stock_categories)
- name (100 chars, nullable)
- code (50 chars, nullable)
- description (text, nullable)
- unit (50 chars, nullable)
- meta (json, nullable)
- timestamps

**stock_balances** (2023_07_31_130325_create_stock_balances_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- stock_item_id (foreign key to stock_items)
- quantity (decimal 25,5, default 0)
- unit_price (decimal 25,5, default 0)
- meta (json, nullable)
- timestamps

**stock_purchases** (2023_07_31_130345_create_stock_purchases_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- number_format (string, nullable)
- number (string, nullable)
- prefix (string, nullable)
- suffix (string, nullable)
- date (date, nullable)
- vendor (100 chars, nullable)
- total (decimal 25,5, default 0)
- description (text, nullable)
- user_id (foreign key to users, nullable)
- meta (json, nullable)
- timestamps

**stock_requisitions** (2023_07_31_130385_create_stock_requisitions_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- number_format (string, nullable)
- number (string, nullable)
- prefix (string, nullable)
- suffix (string, nullable)
- date (date, nullable)
- employee_id (foreign key to employees, nullable)
- description (text, nullable)
- status (20 chars, nullable)
- user_id (foreign key to users, nullable)
- meta (json, nullable)
- timestamps

**stock_transfers** (2023_07_31_130438_create_stock_transfers_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- number_format (string, nullable)
- number (string, nullable)
- prefix (string, nullable)
- suffix (string, nullable)
- date (date, nullable)
- from_employee_id (foreign key to employees, nullable)
- to_employee_id (foreign key to employees, nullable)
- description (text, nullable)
- user_id (foreign key to users, nullable)
- meta (json, nullable)
- timestamps

**stock_adjustments** (2023_07_31_130448_create_stock_adjustments_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- number_format (string, nullable)
- number (string, nullable)
- prefix (string, nullable)
- suffix (string, nullable)
- date (date, nullable)
- description (text, nullable)
- user_id (foreign key to users, nullable)
- meta (json, nullable)
- timestamps

**stock_item_records** (2023_07_31_130547_create_stock_item_records_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- stock_item_id (foreign key to stock_items)
- model_type (string, nullable)
- model_id (big integer, nullable)
- quantity (decimal 25,5, default 0)
- unit_price (decimal 25,5, default 0)
- amount (decimal 25,5, default 0)
- meta (json, nullable)
- timestamps

### Attendance Management Tables

**student_attendances** (2023_08_03_112752_create_student_attendances_table.php)
- id (primary key)
- uuid (unique index)
- team_id (foreign key to teams, nullable)
- batch_id (foreign key to batches)
- subject_id (foreign key to subjects, nullable)
- session (50 chars, nullable)
- date (date, nullable)
- remarks (text, nullable)
- meta (json, nullable)
- timestamps

## Summary of Additional Tables

Due to the extensive nature of this migration collection (170+ files), here's a summary of the remaining major table categories:

### Visitor & Communication Management
- enquiries, enquiry_records, enquiry_follow_ups
- visitor_logs, gate_passes, call_logs, complaints, complaint_logs

### Academic & Learning Management
- assignments, assignment_submissions, lesson_plans
- syllabuses, syllabus_units, learning_materials
- announcements, audiences

### Employee Management Extended
- attendance_types, employee_attendances, employee_attendance_records
- leave_types, leave_allocations, leave_allocation_records
- leave_requests, leave_request_records
- pay_heads, salary_templates, salary_template_records
- salary_structures, salary_structure_records
- payrolls, payroll_records
- work_shifts, employee_work_shifts, timesheets

### System & Device Management
- devices, class_timings, communications
- certificate_templates, certificates, custom_fields

### Examination System
- exam_terms, exams, exam_grades, exam_assessments
- exam_observations, exam_schedules, exam_records
- online_exams, online_exam_questions, online_exam_submissions
- exam_forms, exam_results

### Transport Management Extended
- transport_stoppages, transport_routes, transport_route_stoppages
- transport_route_passengers, transport_route_records

### Additional Academic Features
- subject_wise_students, batch_subject_records
- meal_logs, meal_log_records, menu_items, meals

### Student Services Extended
- student_fee_payments, fee_refunds, fee_refund_records
- student_diaries, room_allocations, health_records
- transfer_requests, contact_edit_requests

### Content & Media Management
- downloads, galleries, gallery_images
- blogs, queries

### HR & Recruitment
- job_vacancies, job_applications, job_vacancy_records

### Forms & Surveys
- forms, form_fields, form_submissions, form_submission_records

### Educational Resources
- book_lists, online_classes

### Website Management
- site_pages, site_menus, site_blocks

### Timetable Management
- class_timing_sessions, timetables, timetable_records, timetable_allocations

### Additional Features
- view_logs, id_card_templates, trips, trip_participants
- sessions, chats, chat_participants, chat_messages
- incidents, academic_departments, group_members
- temp_storage, program_types

### Recent Updates & Modifications
Several tables have been updated with additional columns:
- periods table: added session_column
- divisions table: added program_column
- programs table: added department_column and program_type_column
- configs table: added team_id column

## Database Design Patterns

This database follows several consistent patterns:

1. **UUID Usage**: Most tables include a uuid field for external references
2. **Team-based Multi-tenancy**: Most tables include team_id for multi-tenant support
3. **Soft Metadata**: JSON meta fields for flexible data storage
4. **Audit Trail**: Consistent use of timestamps (created_at, updated_at)
5. **Hierarchical Data**: Many tables support parent-child relationships
6. **Status Tracking**: Many operational tables include status fields
7. **File Management**: Upload token fields for file handling
8. **Flexible Relationships**: Polymorphic relationships using model_type/model_id

## Total Table Count
Approximately **170+ tables** covering a comprehensive educational management system including:
- User & Permission Management
- Academic Structure (Programs, Courses, Batches, Subjects)
- Student & Employee Management
- Financial & Fee Management
- Library & Inventory Management
- Transport & Vehicle Management
- Examination & Assessment System
- Communication & Event Management
- HR & Payroll System
- Facility & Room Management
- And many more specialized modules
