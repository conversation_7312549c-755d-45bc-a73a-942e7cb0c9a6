ALL TABLES SUMMARY - Laravel Migration Files
===========================================

Generated from 170+ migration files in the workspace.

CORE SYSTEM TABLES:
- users (authentication & user management)
- password_resets, failed_jobs, personal_access_tokens
- activity_log, jobs, teams
- permissions, roles, model_has_permissions, model_has_roles, role_has_permissions

CONTENT MANAGEMENT:
- tags, taggables, comments, configs, medias
- options, todos, templates, failed_login_attempts

ACADEMIC STRUCTURE:
- programs, periods, divisions, courses, batches, subjects, subject_records
- academic_departments, program_types, sessions

FINANCIAL MANAGEMENT:
- ledger_types, ledgers, payment_methods
- transactions, transaction_records, transaction_payments

FEE MANAGEMENT:
- fee_groups, fee_heads, fee_concessions, fee_concession_records
- fee_structures, fee_installments, fee_installment_records, fee_allocations
- transport_circles, transport_fees, transport_fee_records
- student_fees, student_fee_records, student_fee_payments
- fee_refunds, fee_refund_records

CONTACT & STUDENT MANAGEMENT:
- contacts, registrations, admissions, students, guardians
- transfer_requests, contact_edit_requests, student_diaries

EMPLOYEE MANAGEMENT:
- departments, designations, employees, employee_records
- accounts, documents, qualifications, experiences

ATTENDANCE SYSTEM:
- student_attendances, attendance_types
- employee_attendances, employee_attendance_records

LEAVE MANAGEMENT:
- leave_types, leave_allocations, leave_allocation_records
- leave_requests, leave_request_records

PAYROLL SYSTEM:
- pay_heads, salary_templates, salary_template_records
- salary_structures, salary_structure_records
- payrolls, payroll_records, work_shifts, employee_work_shifts, timesheets

EVENT & CALENDAR:
- holidays, events, correspondences, incharges

VEHICLE MANAGEMENT:
- vehicles, vehicle_travel_records, vehicle_fuel_records, vehicle_service_records

LIBRARY MANAGEMENT:
- books, book_additions, book_copies, book_transactions, book_transaction_records
- book_lists

FACILITY MANAGEMENT:
- blocks, floors, rooms, inventories, room_allocations

STOCK MANAGEMENT:
- stock_categories, stock_items, stock_balances
- stock_purchases, stock_requisitions, stock_transfers, stock_adjustments
- stock_item_records

VISITOR MANAGEMENT:
- enquiries, enquiry_records, enquiry_follow_ups
- visitor_logs, gate_passes, call_logs, complaints, complaint_logs, incidents

ACADEMIC FEATURES:
- assignments, assignment_submissions, lesson_plans
- syllabuses, syllabus_units, learning_materials
- announcements, audiences, subject_wise_students, batch_subject_records

EXAMINATION SYSTEM:
- exam_terms, exams, exam_grades, exam_assessments
- exam_observations, exam_schedules, exam_records
- online_exams, online_exam_questions, online_exam_submissions
- exam_forms, exam_results

TRANSPORT EXTENDED:
- transport_stoppages, transport_routes, transport_route_stoppages
- transport_route_passengers, transport_route_records

MEAL MANAGEMENT:
- menu_items, meals, meal_logs, meal_log_records

SYSTEM MANAGEMENT:
- devices, class_timings, communications
- certificate_templates, certificates, custom_fields
- view_logs, id_card_templates, temp_storage

TIMETABLE SYSTEM:
- class_timing_sessions, timetables, timetable_records, timetable_allocations

HR & RECRUITMENT:
- job_vacancies, job_applications, job_vacancy_records

FORMS & SURVEYS:
- forms, form_fields, form_submissions, form_submission_records

HEALTH MANAGEMENT:
- health_records

CONTENT & MEDIA:
- downloads, galleries, gallery_images, blogs, queries

WEBSITE MANAGEMENT:
- site_pages, site_menus, site_blocks

COMMUNICATION:
- chats, chat_participants, chat_messages

TRIPS & ACTIVITIES:
- trips, trip_participants

GROUPS:
- group_members

USER TOKENS:
- user_tokens

COMMON PATTERNS:
- Most tables have: id, uuid, team_id, timestamps
- JSON meta fields for flexible data storage
- Foreign key relationships with cascade deletes
- Status fields for workflow management
- Upload tokens for file management
- Hierarchical structures with parent_id fields

TOTAL: 170+ tables covering a comprehensive educational management system
